nohup: ignoring input
INFO:     Started server process [168949]
INFO:     Waiting for application startup.
2025-07-25 09:50:57,732 [app.main] INFO: Initializing application
2025-07-25 09:50:57,736 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-07-25 09:50:57,736 [sqlalchemy.engine.Engine] INFO: SELECT DATABASE()
2025-07-25 09:50:57,736 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,736 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,737 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-07-25 09:50:57,737 [sqlalchemy.engine.Engine] INFO: SELECT @@sql_mode
2025-07-25 09:50:57,737 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,737 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,738 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-07-25 09:50:57,738 [sqlalchemy.engine.Engine] INFO: SELECT @@lower_case_table_names
2025-07-25 09:50:57,738 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,738 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,739 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-25 09:50:57,739 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-25 09:50:57,739 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_analysis_gemini`
2025-07-25 09:50:57,739 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`mcc_analysis_gemini`
2025-07-25 09:50:57,739 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,739 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,741 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-25 09:50:57,741 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-07-25 09:50:57,741 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,741 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,743 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-25 09:50:57,743 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-07-25 09:50:57,743 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,743 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,745 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`gemini_api_log_gemini`
2025-07-25 09:50:57,745 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`gemini_api_log_gemini`
2025-07-25 09:50:57,745 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,745 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,747 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_analysis_gemini`
2025-07-25 09:50:57,747 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_analysis_gemini`
2025-07-25 09:50:57,747 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,747 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,748 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_url_analysis_gemini`
2025-07-25 09:50:57,748 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_url_analysis_gemini`
2025-07-25 09:50:57,749 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,749 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,750 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`risky_keyword_result_gemini`
2025-07-25 09:50:57,750 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`risky_keyword_result_gemini`
2025-07-25 09:50:57,750 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,750 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,752 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`scrape_request_tracker_gemini`
2025-07-25 09:50:57,752 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`scrape_request_tracker_gemini`
2025-07-25 09:50:57,752 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,752 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,753 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`scraped_urls_gemini`
2025-07-25 09:50:57,753 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`scraped_urls_gemini`
2025-07-25 09:50:57,753 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,753 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,754 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`websites_gemini`
2025-07-25 09:50:57,754 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`websites_gemini`
2025-07-25 09:50:57,755 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,755 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,756 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_gemini`
2025-07-25 09:50:57,756 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_analysis_gemini`
2025-07-25 09:50:57,756 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,756 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,758 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_screenshots_gemini`
2025-07-25 09:50:57,758 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_screenshots_gemini`
2025-07-25 09:50:57,758 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,758 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,759 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-25 09:50:57,759 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-07-25 09:50:57,759 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,759 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,761 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-25 09:50:57,761 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-07-25 09:50:57,761 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,761 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,762 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`social_media_analysis_gemini`
2025-07-25 09:50:57,762 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`social_media_analysis_gemini`
2025-07-25 09:50:57,763 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,763 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,764 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-25 09:50:57,764 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-07-25 09:50:57,764 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,764 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,766 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`social_media_profile_result_gemini`
2025-07-25 09:50:57,766 [sqlalchemy.engine.Engine] INFO: DESCRIBE `ds-api-db`.`social_media_profile_result_gemini`
2025-07-25 09:50:57,766 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-25 09:50:57,766 [sqlalchemy.engine.Engine] INFO: [raw sql] {}
2025-07-25 09:50:57,768 INFO sqlalchemy.engine.Engine COMMIT
2025-07-25 09:50:57,768 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-25 09:50:57,768 [app.main] INFO: Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
