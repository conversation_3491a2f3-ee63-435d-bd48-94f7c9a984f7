import asyncio
import json
import time
import csv
import os
from datetime import datetime
import aiohttp
import logging
import sys
from tabulate import tabulate

# Configure minimal logging - warnings only
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger("batch_mcc_test")

# Configuration and constants
API_KEY = "12345678"
API_1_URL = "https://bffapi.biztel.ai/api/analysis/"
API_2_URL = "https://bffapi.biztel.ai/api/analysis/"
API_MCC_RESULTS = "https://bffapi.biztel.ai/api/mcc/results"
REQUEST_TIMEOUT = 6000 # 10 minutes
BATCH_SIZE = 4  # Match server worker count
DELAY_BETWEEN_BATCHES = 30  # seconds between batch requests
MAX_RETRIES_ON_AUTH_ERROR = 5  # Maximum retries for authorization errors

# Single CSV file for all results
RESULTS_FILE = "mcc_batch_results.csv"


# Dictionary of websites with expected MCCs
EXPECTED_MCC = {
   "https://www.firstcry.com/": None,
    "https://www.flipkart.com/": None,
    "https://crowcrowcrow.com": None,
    "https://www.shopelevate.in/": None,
    "https://fishdoctorindia.in": None,
    "https://www.rummycircle.com/": None,
    "https://shopinow.shop/": None,
    "https://amsouls.com/": None,
    "https://beautypalaceshop.com/": None,
    "https://bjkonlinesolutions.com/": None,
    "https://www.gayathrimilk.com": None,
    "https://highbuy.in/": None,
    "https://maishabyashima.com/": None,
    "https://theflamingolife.com/": None,
    "https://unsplash.com/s/photos/model-photography": None,
    "https://www.xave.in": None,
    "https://www.bescom.co.in/bescom/main/home": None,
    "https://crystodent.shop/": None,
    "https://www.justpuregold.com/": None,
    "https://365winks.com/": None,
    "https://aaaexports.com": None,
    "https://academicfight.co.in/": None,
    "https://adeeavee.com": None,
    "https://anantprakashan.com": None,
    "https://app.edumerge.com/": None,
    "https://araviorganic.com/": None,
    "https://arowell.in": None,
    "https://artjunacollection.com": None,
    "https://artofpassage.com/": None,
    "https://artornate.com/": None,
    "https://www.arttodoors.com": None,
    "https://auctiontoys.com/": None,
    "https://bakingcart.com/": None,
    "https://bazonoid.com": None,
    "https://www.bengalipsdstore.in/": None,
    "https://besensitive.in/": None,
    "https://bhagathalwai.com/": None,
    "https://www.birdsofparadyes.com/": None,
    "https://bitesizednovels.com/": None,
    "https://bloomsoom.com/": None,
    "https://blueticklab.com/": None,
    "https://www.booksmagic.in": None,
    "https://botabochi.com/": None,
    "https://brainbucks.in": None,
    "https://www.brandsamor.com": None,
    "https://brothersdelivery.in": None,
    "https://brovyapari.in/": None,
    "https://www.buzzbeautyindia.in/": None,
    "https://capeasymicrofin.in/": None,
    "https://careermize.com/": None,
    "https://casumityadav.com/": None,
    "https://classifix.in/": None,
    "https://classifyiq.com": None,
    "https://clayveda.com": None,
    "https://www.clcsikar.com/": None,
    "https://cleaninger.in": None,
    "https://clixsel.com": None,
    "https://coleo.in": None,
    "https://course.vijayanandbhalerao.com/sales": None,
    "https://coverat99.com/": None,
    "https://crekart.com": None,
    "https://www.crm4mfds.co.in": None,
    "https://cscbazar.in": None,
    "https://customgifts.myphotoprint.in/": None,
    "https://daintylittleshop.com/?_ab=0&_fd=0&_sc=1": None,
    "https://decorow.co/": None,
    "https://desolitaire.com/": None,
    "https://devomkids.com/": None,
    "https://dhruvswhite.com/": None,
    "https://digigoodsstore.in/": None,
    "https://digimall.store": None,
    "https://www.digipoints.in": None,
    "https://digirxy.in": None,
    "https://digixsell.in": None,
    "https://dirtsstore.com/": None,
    "https://dobraindia.com/": None,
    "https://www.dreamvazaar.com": None,
    "https://drynotch.com": None,
    "https://eachdaykart.in/": None,
    "https://www.ecomempires.in/": None,
    "https://ecoop.in": None,
    "https://eeko.ai/": None,
    "https://www.entreprenext.com/": None,
    "https://www.epaal.in/": None,
    "https://eunoiabysimran.com/": None,
    "https://exitohub.com": None,
    "https://www.faab.in": None,
    "https://fabricfactory.in/": None,
    "https://floved.in/": None,
    "https://www.formis.org": None,
    "https://www.freelancingwithumesh.com/": None,
    "https://fuelthefire.in/": None,
    "https://www.funnybones.in/": None,
    "https://www.gangarealty.com/": None,
    "https://www.gardenofjoy.in": None,
    "https://getorganics.in/": None,
    "https://girlsdontdressforboys.com/": None,
    "https://globalsecareer.com/": None,
    "https://gocareers.in": None,
    "https://www.goetc.in/services": None,
#risky starts form here
   "https://www.rummycircle.com/": None,
   "https://cardgames.io/rummy/": None,
   "https://www.playrummy-online.com/": None,
   "https://www.jungleerummy.com/": None,
    "https://crypto.com/": None,
    "https://www.coinbase.com/en-gb/": None,
    "https://coinmarketcap.com/": None,
    "https://wazirx.com/": None,
    "https://www.binance.com/en": None,
    "https://hubshooting.com/": None,
    "https://www.guns.com/": None,
    "https://www.bundook.in/": None,
    "https://www.gotoliquorstore.com/": None,
    "https://www.thewhiskyexchange.com/": None,
    "https://www.madhuloka.com/": None,
    "https://www.luxuriousdrinks.com/en/": None,
    "https://www.godfreyphillips.co.in/": None,
    "https://www.vsthyd.com/mainsite/": None,
    "https://www.ntcind.com/": None,
    "https://www.goldentobacco.in/": None,
    # "https://sinnarbidi.com/": None,
    # "https://citizenbyklutch.com/": None,
    # "https://zazagreen.com/": None,
    # "https://cambridgecannabiscompany.com/": None,
    # "https://astralmarkets.com/": None,
    # "https://earthandivy.co/": None,
    # "https://www.quidich.com/": None,
    # "https://asteria.co.in/": None,
    # "https://ideaforgetech.com/": None,
    # "https://aero360.co.in/": None,
    # "https://www.garudaaerospace.com/": None,
    # "https://www.truemeds.in/": None,
    # "https://www.apollopharmacy.in/": None,
    # "https://pharmeasy.in/": None,
    # "https://www.netmeds.com/": None,
    # "https://www.1mg.com/": None,
    # "https://goldenpatelson.in/": None,
    # "https://aquaherbals.in/": None,
    # "https://buybhangonline.com/": None,
    # "https://www.pidilite.com/": None,
    # "https://www.gfl.co.in/": None,
    # "https://www.godeepak.com/": None,
    # "https://www.tatachemicals.com/": None,
    # "https://vinatiorganics.com/": None,
    # "https://in.seekerpleasure.com/": None,
    # "https://www.simpleescorts.in/": None,
    # "https://www.sweetsecrets.biz/": None,
    # "https://www.isabasu.com/": None,
    # "https://www.itspleazure.com/": None,
    # "https://www.kaamastra.com/": None,
    # "https://naughtynights.in/": None,
    # "https://www.adultscare.com/": None,
    # "https://www.bukalapak.com/": None,
    # "https://m.pinduoduo.com/": None,
    # "https://www.taobao.com/": None,
    # "https://www.tokopedia.com/": None,
    # "https://lotto-india.com/": None,
    # "https://www.lottosmile.in/": None,
    # "https://bookmyrajshree.com/": None,
    # "https://lottery.maharashtra.gov.in/index.html": None,
    # "https://india.1xbet.com/slots": None,
    # "https://www.casino.org/india/": None,
    # "https://www.thetopbookies.com/india-casinos": None,
    # "https://pari-match-in.com/en/": None,
    # "https://batery-bet.in/": None,
    # "https://casino.netbet.com/in/": None,
}

EXPECTED_MCC1 = {
     "https://www.epaal.in/": None,
     "https://growwithgautamjain.com/?page_id=2098": None,
     "https://www.healeasy.in/": None,
     "https://www.hedoag.com/": None,
     "https://www.highstar.in/": None,
    # "https://hikabae.com/": None,
    # "https://www.hireforskillz.com": None,
    # "https://www.hobbycorner.in": None,
    # "https://www.homekouzina.com": None,
    # "https://homeplant.in/": None,
    # "https://hostiware.com/": None,
    # "https://www.houseinnovate.com": None,
    # "https://www.houseofaks.in": None,
    # "https://houseofheritage.in/": None,
    # "https://hoverpro.in/": None,
    # "https://htcl.co.in": None,
    # "https://www.hungrymoments.com/": None,
    # "https://hushbaby.in": None,
    # "https://iedindia.com/": None,
    # "https://www.inproxy.store/": None,
    # "https://www.intelliclick.in": None,
    # "https://internetsoil.com/": None,
    # "https://intrustfinanceindia.com/": None,
    # "http://www.iriesalad.in": None,
    # "https://www.itraake.com": None,
    # "https://jawaadtradingacademy.com/": None,
    # "https://kelikunj.com/": None,
    # "https://kidsneed.in/": None,
    # "https://www.kingsolympiad.in/": None,
    # "https://kitchenxpress.net/": None,
    # "https://lilybelle.store/": None,
    # "https://live.abundanceliving.in/register-supermanifestor": None,
    # "https://makazi.in/": None,
    # "https://marutiresearch.in/": None,
    # "https://www.maycoup.in/": None,
    # "https://mechdeals.com/": None,
    # "https://www.metroonline.in": None,
    # "https://www.mindfulpsychiatristindore.com": None,
    # "https://mirayazjaipur.com/": None,
    # "https://mizojerseyhome.in/": None,
    # "https://muslimwears.com/": None,
    # "https://www.mypresets.in": None,
    # "https://nalaaorganic.com": None,
    # "https://nanikapitara.in/": None,
    # "https://natureplus.milkmaster.co/": None,
    # "https://ninety-minutes.in/": None,
    # "https://oceglow.com/": None,
    # "https://officialindian.shop": None,
    # "https://omshivaayfoods.com/": None,
    # "https://onecloth.in/": None,
    # "https://onlinesyndrome.com": None,
    # "https://www.onlyblackb2b.com": None,
    # "https://onohosting.com/": None,
    # "https://pa1innovsource.com/": None,
    # "https://parijatawaterro.com/": None,
    # "https://parthjethva.store": None,
    # "https://pdtce.com/": None,
    # "https://www.perchofficial.com": None,
    # "https://www.prashanthayoga.com": None,
    # "https://primepick.co.in/": None,
    # "https://pushmycart.in": None,
    # "https://rajanyas.com/": None,
    # "https://ravindrabharti.com": None,
    # "https://rawatstore.in/": None,
    # "https://rbdbooks.in": None,
    # "https://relaxcompany.online": None,
    # "https://www.rerunn.com": None,
    # "https://researchpeer.network/": None,
    # "https://restarthfipl.in/": None,
    # "https://ribbonofsummer.com/": None,
    # "https://www.riyadesignshop.com/": None,
    # "https://www.robozar.com": None,
    # "https://rrethnix.in/": None,
    # "https://samah.in/": None,
    # "https://scentsationindia.com/": None,
    # "https://schoolcanvas.com": None,
    # "https://www.screenhub.in": None,
    # "https://www.sdcampus.com": None,
    # "https://serpzilla.com/": None,
    # "https://www.shaanfinance.in/": None,
    # "https://shesellsbykaynat.com/": None,
    # "https://shop.thegauravjain.com": None,
    # "https://shopbodyssey.com/": None,
    # "https://shvasa.com": None,
    # "https://www.sindhuherbals.com": None,
    # "https://sipologie.in/": None,
    # "https://skapg.in/": None,
    # "https://www.sockscarving.co.in": None,
    # "https://softwarehuntt.co": None,
    # "https://solvedudar.com": None,
    # "https://soulsanskar.com/": None,
    # "https://www.sovamayurveda.com": None,
    # "https://www.sowfihost.com": None,
    # "https://spvaigautomobiles.com/": None,
    # "https://sshospitalangul.com/": None,
    # "https://www.stickermart.in": None,
    # "https://stickyfunk.xyz/": None,
    # "https://stockhealth.in/": None,
    # "https://stooky.in/": None,
    # "https://store.quantumbytestudios.in/": None,
    # "https://studyabacus.com": None,
    # "https://superclo.com/": None,
    # "https://www.superergo.in": None,
    # "https://www.sustainability101.in/": None,
    # "https://www.swadeshiblessings.in": None,
    # "https://swarnbharatorganics.com": None,
    # "https://targetuppsc.com/": None,
    # "https://technoistics.com/": None,
    # "https://www.tekraadigital.com": None,
    # "https://tgmacad.com": None,
    # "https://thecubeofficial.com/": None,
    # "https://thefreeman.org/": None,
    # "https://theglasscouture.com": None,
    # "https://theguidestreet.com/": None,
    # "https://thekamlesh.com/": None,
    # "https://www.thekitkart.com": None,
    # "https://www.thelearnyn.com/": None,
    # "https://www.themarketmood.com/": None,
    # "https://themodernart.in/": None,
    # "https://www.thewearcart.com/": None,
    # "https://thunipeedika.in/": None,
    # "https://tijori.site/": None,
    # "https://tingd2c.com": None,
    # "https://togaz.in/": None,
    # "https://www.totebae.com/": None,
    # "https://www.tradegold.in/": None,
    # "https://trendzbytanya.com/": None,
    # "https://troyo.in": None,
    # "https://urbanfashionpeople.com": None,
    # "https://vedhafishfarm.com": None,
    # "https://www.vembasshoppie.com/": None,
    # "https://veroforza.com/": None,
    # "https://www.vikifurniture.com/": None,
    # "https://vimayamdhotis.com": None,
    # "https://virtualshopi.com/": None,
    # "https://www.virtualvault.life/": None,
    # "https://vishnumane.com/": None,
    # "https://vitalylabs.com": None,
    # "https://vmantiquedecor.in": None,
    # "https://www.voi11.com": None,
    # "https://vosa.org.in/": None,
    # "https://vrajbhoomi.in/": None,
    # "https://www.walletadda.in/": None,
    # "https://wallmantra.com": None,
    # "https://wealthmultiplier.org/course-page-new": None,
    # "https://wingsofdesign.com/": None,
    # "https://worldbaniyaforum.com/": None,
    # "https://www.zamsfashion.in/": None,
    # "https://ziippykart.com/": None,
    # "https://zilotyshopping.com/": None,
    # "https://zobbipayindia.com": None,
    # "https://365winks.com/": None
}











class WebsiteResult:
    def __init__(self, website, request_time=None):
        self.website = website
        self.request_time = request_time
        self.ref_id = None
        self.mcc = None
        self.status = "PENDING"
        self.time_taken = None
        self.expected_mcc = EXPECTED_MCC.get(website)
    
    def to_row(self):
        return [
            self.website,
            self.mcc if self.mcc is not None else "None",
            self.status,
            f"{self.time_taken:.2f}s" if self.time_taken is not None else "N/A",
            self.ref_id if self.ref_id is not None else "None"
        ]
    
    def to_dict(self):
        return {
            "website": self.website,
            "mcc": self.mcc,
            "status": self.status,
            "timetaken": self.time_taken if self.time_taken is not None else "N/A",
            "refid": self.ref_id
        }

async def request_analysis(session, website):
    """Send a request to the API to analyze a website"""
    payload = {"website": website, "registeredEntityName": ""}
    headers = {"X-API-KEY": API_KEY, "Content-Type": "application/json"}

    result = WebsiteResult(website, datetime.now())

    # Auth error retry loop
    for retry in range(MAX_RETRIES_ON_AUTH_ERROR + 1):
        try:
            async with session.post(API_1_URL, json=payload, headers=headers, timeout=REQUEST_TIMEOUT) as response:
                # Handle specific HTTP error codes
                if response.status == 401:
                    if retry < MAX_RETRIES_ON_AUTH_ERROR:
                        await asyncio.sleep(2)
                        continue
                    else:
                        result.status = "AUTH_ERROR"
                        await update_csv_realtime(result)
                        return result
                elif response.status == 429:
                    result.status = "RATE_LIMITED"
                    await update_csv_realtime(result)
                    return result
                elif response.status != 200:
                    result.status = f"REQUEST_FAILED_{response.status}"
                    await update_csv_realtime(result)
                    return result

                try:
                    response_data = await response.json()
                    if response_data.get("success"):
                        result.ref_id = response_data.get("refId")
                        # Update CSV immediately with pending status
                        await update_csv_realtime(result)
                        return result
                    else:
                        error_msg = response_data.get("message", "Unknown error")
                        result.status = f"API_ERROR_{error_msg[:20]}"
                        await update_csv_realtime(result)
                        return result
                except json.JSONDecodeError:
                    result.status = "INVALID_JSON"
                    await update_csv_realtime(result)
                    return result
        except Exception as e:
            result.status = f"ERROR_{str(e)[:30]}"
            await update_csv_realtime(result)
            return result

    result.status = "MAX_RETRIES_EXCEEDED"
    await update_csv_realtime(result)
    return result

async def update_csv_realtime(result):
    """Update CSV file in real-time with current result"""
    try:
        # Check if file exists and has header
        file_exists = os.path.exists(RESULTS_FILE)

        # Read existing data to check if this website already exists
        existing_data = {}
        if file_exists:
            try:
                with open(RESULTS_FILE, 'r', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        existing_data[row['website']] = row
            except Exception:
                pass

        # Update or add the current result
        existing_data[result.website] = {
            'website': result.website,
            'mcc': result.mcc if result.mcc is not None else "None",
            'status': result.status,
            'timetaken': f"{result.time_taken:.2f}" if result.time_taken is not None else "N/A",
            'refid': result.ref_id if result.ref_id is not None else "None"
        }

        # Write all data back to file
        with open(RESULTS_FILE, 'w', newline='') as f:
            fieldnames = ['website', 'mcc', 'status', 'timetaken', 'refid']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for data in existing_data.values():
                writer.writerow(data)

    except Exception as e:
        logger.warning(f"Failed to update CSV for {result.website}: {str(e)}")

async def get_analysis_result(session, ref_id):
    """Get the analysis result for a given ref_id"""
    headers = {"X-API-KEY": API_KEY}
    
    # Try both endpoints regardless of success to be thorough
    primary_response = None
    mcc_response = None
    
    # Try the primary endpoint
    try:
        async with session.get(f"{API_2_URL}{ref_id}", headers=headers, timeout=REQUEST_TIMEOUT) as response:
            if response.status == 200:
                try:
                    primary_response = await response.json()
                except json.JSONDecodeError:
                    pass
            elif response.status == 401:
                # Auth error, don't retry here as this isn't the initial request
                return {"success": False, "status": "AUTH_ERROR"}
    except Exception:
        pass
    
    # Always try the MCC endpoint as well, regardless of primary success
    try:
        async with session.get(f"{API_MCC_RESULTS}/{ref_id}", headers=headers, timeout=REQUEST_TIMEOUT) as response:
            if response.status == 200:
                try:
                    mcc_data = await response.json()
                    # Format consistent with primary endpoint
                    mcc_response = {
                        "success": True,
                        "status": mcc_data.get("status", "UNKNOWN"),
                        "mccResult": {
                            "mcc": mcc_data.get("mcc", None)
                        }
                    }
                except json.JSONDecodeError:
                    pass
            elif response.status == 401:
                if not primary_response:  # Only return auth error if primary didn't succeed
                    return {"success": False, "status": "AUTH_ERROR"}
    except Exception:
        pass
    
    # Now determine which result to return
    # Prefer completed results from either endpoint
    if primary_response and primary_response.get('status') == 'COMPLETED':
        return primary_response
    elif mcc_response and mcc_response.get('status') == 'COMPLETED':
        return mcc_response
    # If we have a primary response with any status, use that
    elif primary_response:
        return primary_response
    # Otherwise use MCC response if available
    elif mcc_response:
        return mcc_response
    # If nothing worked, return None
    return None

async def check_results(session, results, result_queue):
    """Check results for all pending websites"""
    pending_results = [r for r in results if r.status == "PENDING" and r.ref_id is not None]

    # Log the number of pending results we're checking
    print(f"\rChecking {len(pending_results)} pending results...", end="", flush=True)

    for result in pending_results:
        try:
            response = await get_analysis_result(session, result.ref_id)

            if response and isinstance(response, dict):
                # Calculate time from initial request
                current_time = datetime.now()
                elapsed = (current_time - result.request_time).total_seconds()

                # Handle auth errors
                if response.get('status') == 'AUTH_ERROR':
                    result.status = "AUTH_ERROR"
                    result.time_taken = elapsed
                    await update_csv_realtime(result)
                    await result_queue.put(result)
                    continue

                # Check for COMPLETED status regardless of success flag
                if response.get('status') == 'COMPLETED':
                    result.status = "COMPLETED"
                    result.time_taken = elapsed
                    mcc_result = response.get('mccResult', {})
                    result.mcc = mcc_result.get('mcc')
                    await update_csv_realtime(result)
                    await result_queue.put(result)
                    continue

                # Handle other statuses when success is True
                if response.get('success'):
                    status = response.get('status')

                    # Only update status if it's definitive (not still processing)
                    if status == 'FAILED' or status == 'ERROR':
                        result.status = status
                        result.time_taken = elapsed
                        # Get any error message
                        if 'message' in response:
                            result.status = f"{status}_{response['message'][:20]}"
                        await update_csv_realtime(result)
                        await result_queue.put(result)
                        continue
                    # For other statuses (e.g. PROCESSING, PENDING) keep waiting

                # Check error messages for clues
                if not response.get('success') and 'message' in response:
                    # If it's a non-recoverable error, mark it and continue
                    message = response.get('message', '')
                    if 'not found' in message.lower() or 'invalid' in message.lower():
                        result.status = f"ERROR_{message[:20]}"
                        result.time_taken = elapsed
                        await update_csv_realtime(result)
                        await result_queue.put(result)
                        continue
        except Exception as e:
            # On exception, just continue checking other websites
            logger.warning(f"Exception checking result for {result.website}: {str(e)}")
            pass

async def display_progress(total, result_queue):
    """Display progress - CSV updates are handled in real-time by update_csv_realtime"""
    completed = 0

    while completed < total:
        result = await result_queue.get()
        completed += 1

        # Print progress with more details
        status_color = "✓" if result.status == "COMPLETED" else "✗" if result.status.startswith("ERROR") else "⏳"
        print(f"\r{status_color} Progress: {completed}/{total} ({completed/total*100:.1f}%) - Latest: {result.website[:50]}{'...' if len(result.website) > 50 else ''}", end="", flush=True)

    print("\nAll results collected!")

async def send_batch_requests(session, websites, result_queue):
    """Send a batch of website requests in parallel"""
    all_results = []

    for i in range(0, len(websites), BATCH_SIZE):
        batch = websites[i:i+BATCH_SIZE]
        batch_start_time = time.time()

        # Process each batch in parallel
        batch_tasks = [request_analysis(session, website) for website in batch]
        batch_results = await asyncio.gather(*batch_tasks)

        for result in batch_results:
            all_results.append(result)

            # All results are now updated in CSV in real-time, so put all in queue
            if result.status != "PENDING":
                await result_queue.put(result)

        # Print batch info
        batch_time = time.time() - batch_start_time
        print(f"\rSent batch {i//BATCH_SIZE + 1}/{(len(websites) + BATCH_SIZE - 1)//BATCH_SIZE} " +
              f"({i+1}-{min(i+BATCH_SIZE, len(websites))}/{len(websites)}) in {batch_time:.2f}s",
              end="", flush=True)

        # Delay between batches (unless it's the last batch)
        if i + BATCH_SIZE < len(websites):
            await asyncio.sleep(DELAY_BETWEEN_BATCHES)

    print("\nAll website requests sent!")
    return all_results

async def main():
    # Initialize the single CSV file
    if os.path.exists(RESULTS_FILE):
        print(f"Found existing results file: {RESULTS_FILE}")
        print("Results will be updated in real-time. Past records will be maintained.")
    else:
        print(f"Creating new results file: {RESULTS_FILE}")
        # Create empty CSV with headers
        with open(RESULTS_FILE, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['website', 'mcc', 'status', 'timetaken', 'refid'])

    # Get list of websites
    websites = list(EXPECTED_MCC.keys())
    total_websites = len(websites)

    # Calculate a reasonable total timeout
    # If server processes 4 at a time and each takes ~15 minutes worst case,
    # total time would be around (total_websites / 4) * 15 minutes + buffer
    estimated_server_process_time = (total_websites / 4) * 900  # 15 minutes per website
    # Add buffer for network delays, retries, etc.
    TOTAL_TIMEOUT = max(estimated_server_process_time * 1.5, 3600)  # at least 1 hour

    print(f"Starting batch MCC test for {total_websites} websites")
    print(f"Batch size: {BATCH_SIZE} websites every {DELAY_BETWEEN_BATCHES} seconds")
    print(f"Estimated total runtime: {TOTAL_TIMEOUT/60:.1f} minutes (based on server processing 4 sites at a time)")
    print(f"Results will be updated in real-time to: {RESULTS_FILE}")

    # Create a queue for completed results
    result_queue = asyncio.Queue()

    # Create TCP connector with appropriate settings
    tcp_connector = aiohttp.TCPConnector(
        keepalive_timeout=60,
        limit=100,
        ttl_dns_cache=300
    )

    session_timeout = aiohttp.ClientTimeout(total=None, sock_connect=60, sock_read=60)

    # Start progress display task
    progress_task = asyncio.create_task(display_progress(total_websites, result_queue))
    
    async with aiohttp.ClientSession(connector=tcp_connector, timeout=session_timeout) as session:
        # First phase: Send all requests in batches
        batch_start_time = time.time()
        print("Phase 1: Sending requests to all websites in batches...")
        
        all_results = await send_batch_requests(session, websites, result_queue)
        
        batch_time = time.time() - batch_start_time
        print(f"All {total_websites} websites requested in {batch_time:.2f} seconds")
        
        # Second phase: Poll for results
        print("Phase 2: Collecting results...")
        print(f"The server processes 4 websites at a time, so results will come in batches")
        
        # Keep checking until all results are complete or timeout
        check_interval = 60  # Check every 60 seconds
        
        # Start the timer for the overall process
        start_time = time.time()
        last_status_time = start_time
        
        while any(r.status == "PENDING" for r in all_results):
            await check_results(session, all_results, result_queue)
            
            # Print status every 2 minutes
            current_time = time.time()
            if current_time - last_status_time > 120:
                pending_count = sum(1 for r in all_results if r.status == "PENDING")
                completed_count = total_websites - pending_count
                elapsed_minutes = (current_time - start_time) / 60
                print(f"\nStatus after {elapsed_minutes:.1f} minutes: {completed_count}/{total_websites} completed, {pending_count} pending")
                last_status_time = current_time
            
            # Check if we've exceeded our total time budget
            if time.time() - start_time > TOTAL_TIMEOUT:
                pending_count = sum(1 for r in all_results if r.status == "PENDING")
                print(f"\nTotal timeout of {TOTAL_TIMEOUT/60:.1f} minutes exceeded. Still have {pending_count} pending results.")
                break
                
            # Only sleep if we still have pending results
            if any(r.status == "PENDING" for r in all_results):
                await asyncio.sleep(check_interval)
        
        # Mark any remaining pending results as timed out
        for result in all_results:
            if result.status == "PENDING":
                result.status = "TIMEOUT_EXCEEDED"
                result.time_taken = (datetime.now() - result.request_time).total_seconds()
                await update_csv_realtime(result)
                await result_queue.put(result)

    # Wait for progress display to complete
    await progress_task

    # Calculate statistics
    completed_count = sum(1 for r in all_results if r.status == "COMPLETED")
    failed_count = sum(1 for r in all_results if r.status != "COMPLETED")

    print("\nTest Results Summary:")
    print(f"Total websites: {total_websites}")
    print(f"Completed: {completed_count}")
    print(f"Failed: {failed_count}")

    # Print results table
    table_data = [r.to_row() for r in all_results]
    print("\nResults Table:")
    print(tabulate(table_data[:10], headers=['website', 'mcc', 'status', 'timetaken', 'refid'], tablefmt='grid'))
    print("... (showing first 10 rows, all results in CSV file)")

    print(f"\nAll results have been updated in real-time to: {RESULTS_FILE}")
    print("The CSV file contains the most up-to-date results with past records maintained.")

if __name__ == "__main__":
    asyncio.run(main()) 
